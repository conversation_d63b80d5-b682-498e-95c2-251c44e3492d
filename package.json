{"name": "student", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@mui/icons-material": "^5.11.16", "@mui/material": "^5.12.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "bootstrap": "^5.2.3", "chart.js": "^4.2.1", "ng2-charts": "^4.1.1", "react": "^18.2.0", "react-bootstrap": "^2.7.2", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.9.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}